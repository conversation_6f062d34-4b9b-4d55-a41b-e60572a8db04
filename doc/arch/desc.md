

1. Conversation Management System
   The core of the application is built around a conversation management system, primarily implemented in
   src/conv/conv_mgr.py. This system handles:
   Key Components:
   ConvManager Class: Central class that manages the lifecycle of conversations
   Metadata Handling: Processes and stores conversation metadata
   Event Management: Uses events to coordinate activities (e.g., meta_updated, conversation_ended)
   Agent Integration: Connects with AI agents to handle the conversation flow
   Main Features:
   Session activity tracking and idle detection
   Conversation state management
   Event-based architecture for handling speech events
   Metrics collection and reporting
   Graceful shutdown and cleanup
2. Configuration Management
   The application uses a robust configuration system (src/app/config.py) that:
   Loads settings from environment variables
   Provides typed configuration objects
   Supports different environments (development, production)
   Implements a singleton pattern for config access via get_config()
3. API Integration Services
   The ConvSvc class (src/conv/conv_svc.py) provides a standardized way to interact with backend services:
   Authentication handling
   Error capturing and reporting
   Sentiment analysis integration
   Conversation state updates
   Callback scheduling
   Follow-up management
4. Factory Pattern Implementation
   The codebase uses factory patterns to create various components:
   TTS Factory (src/factory/tts_factory.py): Creates text-to-speech services based on configuration
   STT Factory (src/factory/stt_factory.py): Creates speech-to-text services with appropriate language settings
   These factories abstract away the complexity of creating these components and allow for easy switching between
   providers
5. Audio Processing Utilities
   Common audio processing functions are available in multiple places:
   Audio resampling
   Volume calculation
   Audio mixing and manipulation
   Format conversion (PCM, μ-law)
6. Metadata Models
   The RoomConfig class in src/conv/conv_meta.py provides a structured way to:
   Define conversation metadata
   Validate configuration
   Load/parse JSON data
   Manage conversation context
7. Logging and Monitoring
   The application has comprehensive logging and monitoring:
   Sentry integration for error tracking
   Structured logging
   Performance monitoring with spans
   Metrics collection
8. Event-Driven Architecture
   The system uses an event-driven approach:
   Event handlers for speech events (start/stop speaking)
   Asynchronous task management
   Event-based state transitions
9. Error Handling
   Robust error handling is implemented throughout:
   Exception capturing and reporting
   Graceful degradation
   Sentry integration for error tracking
   Decorators for consistent error handling (@capture_errors, @sentry_span)
10. Utility Functions
    Common utility functions include:
    Time handling (get_now(), get_now_s())
    JSON processing
    String manipulation
    Asynchronous task management
    This architecture provides a flexible, maintainable system for managing voice conversations with AI agents, with
    strong error handling, configuration management, and service integration capabilities.

----

1. Production deployment stabilization
2. LiveKit v1.0.0rc3
3. local STT install
4. 