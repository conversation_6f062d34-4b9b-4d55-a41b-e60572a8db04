import asyncio
import logging
from contextvars import ContextVar

import sentry_sdk
from livekit.agents import AutoSubscribe, JobContext, WorkerOptions, cli, JobProcess, RoomInputOptions
from livekit.plugins import noise_cancellation

from app.config import get_config
from app.sentry_config import initialize_sentry, capture_errors
from log.sentry_decorators import sentry_transaction, sentry_span

conversation_id_var = ContextVar('conversation_id',default="")

# Initialize configuration and log
config = get_config()
logging.basicConfig(
    level=config.app.log_level,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),  # This ensures output to console
        logging.FileHandler('app.log')  # This will also save logs to a file
    ]
)
logger = logging.getLogger(__name__)
# logger.propagate = False

initialize_sentry(config)

from conv.conv_mgr import ConvManager

def prewarm(job_process: JobProcess) -> None:
    # vad_profile = get_vad_profile("phone_call")
    # job_process.userdata["vad"] = silero.VAD.load(**vad_profile.to_dict())
    pass


async def get_or_wait_for_metadata(ctx: JobContext, conversation_manager: ConvManager):
    # Create a future that will be resolved when metadata is updated
    future = asyncio.get_event_loop().create_future()
    if len(ctx.room.metadata) > 0:
        future.set_result(ctx.room.metadata)
        return future.result()

    @ctx.room.on("room_metadata_changed")
    def room_metadata_changed(old_metadata, metadata):
        logger.debug(f"Room metadata changed: {metadata}")
        if metadata:
            if not future.done():
                future.set_result(metadata)

    try:
        await asyncio.wait_for(future, timeout=1000.0)  # 10 second timeout
        return future.result()  # Return the metadata
    except asyncio.TimeoutError:
        logger.warning("Timed out waiting for metadata to be updated")
        return ctx.room.metadata  # Return current metadata even if timeout occurred




@capture_errors
@sentry_transaction("conversation","entry point")
async def entrypoint(ctx: JobContext) -> None:
    scope = sentry_sdk.get_isolation_scope()
    logger.info(f"Room Name: {ctx.room.name}")
    await ctx.connect(auto_subscribe=AutoSubscribe.AUDIO_ONLY)

    try:
        conversation_manager = ConvManager(ctx)
        conversation_manager.register_shutdown_callback()
        is_inbound_call = ctx.room.name.startswith("inbound-call")
        logger.info(f"is_inbound_call: {is_inbound_call}")
        metadata = await get_or_wait_for_metadata(ctx, conversation_manager)
        conversation_manager.update_metadata(metadata)
    except Exception as e:
        logger.error(f"Failed to initialize ConvManager: {str(e)}")
        logger.error(f"Current room metadata: {ctx.room.metadata!r}")
        raise

    await run_conversation(ctx, conversation_manager)


@sentry_span(op="conversation", description="run conversation")
async def run_conversation(ctx: JobContext, conversation_manager: ConvManager):
    # Start the conversation
    jana = await conversation_manager.start_conversation()

    participant = await conversation_manager.wait_for_participant()
    await conversation_manager.session.start(room=ctx.room, agent=jana, room_input_options=RoomInputOptions(
        noise_cancellation=noise_cancellation.BVCTelephony(),
    ))

    # Metrics collection is now handled automatically by ConvManager
    await jana.say_welcome()


def main() -> None:
    worker_options = WorkerOptions(entrypoint_fnc=entrypoint, prewarm_fnc=prewarm,
                                   initialize_process_timeout=120,
                                   )
    try:
        logger.info("Starting application")
        cli.run_app(worker_options)
    except asyncio.CancelledError:
        logger.info("Long-running task was cancelled")
    except Exception as e:
        logger.error(f"Error in main application: {str(e)}", exc_info=True)
        sentry_sdk.capture_exception(e)  # Capture unexpected exceptions
    finally:
        logger.info("Shutting down the application.")

# Entry point
if __name__ == "__main__":
    main()