import os
from typing import Optional

from dotenv import load_dotenv
from pydantic import Field
from pydantic_settings import BaseSettings

load_dotenv()
_config = None
os.environ["DYLD_LIBRARY_PATH"] = (
    "/opt/homebrew/Cellar/ffmpeg/7.0.1/lib:/opt/homebrew/lib"
)

# Core API configuration
class CoreAPISettings(BaseSettings):
    url: str = Field(..., alias="CORE_API_URL")
    login: str = Field(..., alias="CORE_API_LOGIN")
    password: str = Field(..., alias="CORE_API_PASSWORD")

# Conversation API configuration
class ConversationAPISettings(BaseSettings):
    url: str = Field(..., alias="CONVERSATION_API_URL")
    login: str = Field(..., alias="CONVERSATION_API_LOGIN")
    password: str = Field(..., alias="CONVERSATION_API_PASSWORD")

# LiveKit configuration
class LiveKitSettings(BaseSettings):
    url: str = Field(..., alias="LIVEKIT_URL")
    api_key: str = Field(..., alias="LIVEKIT_API_KEY")
    api_secret: str = Field(..., alias="LIVEKIT_API_SECRET")

# Langfuse configuration
class LangfuseSettings(BaseSettings):
    public_key: str = Field(..., alias="LANGFUSE_PUBLIC_KEY")
    secret_key: str = Field(..., alias="LANGFUSE_SECRET_KEY")
    host: Optional[str] = Field(None, alias="LANGFUSE_HOST")
    enable_trace: Optional[bool] = Field(False, alias="LANGFUSE_ENABLE_TRACE")

# OpenAI configuration
class OpenAISettings(BaseSettings):
    api_key: str = Field(..., alias="OPENAI_API_KEY")
    sentiment_analysis_model: str = Field("gpt-3.5-turbo", alias="SENTIMENT_ANALYSIS_MODEL")
    llm_model: str = Field("gpt-4o", alias="LLM_MODEL")
    temperature: float = Field(0.3, alias="TEMPERATURE")


# PlayAi configuration
class PlayAiSettings(BaseSettings):
    api_key: str = Field(..., alias="PLAYHT_API_KEY")
    user_id: str = Field(..., alias="PLAYHT_USER_ID")

# ElevenLabs configuration
class ElevenLabsSettings(BaseSettings):
    api_key: str = Field(..., alias="ELEVENLABS_API_KEY")
    stability: float = Field(0.7, alias="ELEVENLABS_STABILITY")
    similarity_boost: float = Field(0.75, alias="ELEVENLABS_SIMILARITY_BOOST")
    style: float = Field(0, alias="ELEVENLABS_STYLE")
    use_speaker_boost: bool = Field(True, alias="ELEVENLABS_USE_SPEAKER_BOOST")

# Deepgram configuration
class DeepgramSettings(BaseSettings):
    api_key: str = Field(..., alias="DEEPGRAM_API_KEY")
    model: str = Field("nova-3", alias="DEEPGRAM_MODEL")


# AssemblyAI configuration
class AssemblyAISettings(BaseSettings):
    api_key: Optional[str] = Field(..., alias="ASSEMBLYAI_API_KEY")
    model: Optional[str] = Field("default", alias="ASSEMBLYAI_MODEL")

# Application settings
class AppSettings(BaseSettings):
    log_level: str = Field("INFO", alias="LOG_LEVEL")
    voice_activity_timeout: float = Field(5.0, alias="VOICE_ACTIVITY_TIMEOUT")
    system_prompt_refresh_interval: int = Field(5, alias="SYSTEM_PROMPT_REFRESH_INTERVAL")
    first_message: str = Field("Hey", alias="FIRST_MESSAGE")
    last_message: str = Field("Goodbye (:", alias="LAST_MESSAGE")
    sentry_dsn: Optional[str] = Field(None, alias="SENTRY_DSN")
    sentry_environment: Optional[str] = Field(None, alias="SENTRY_ENVIRONMENT")
    stt_provider: Optional[str] = Field("deepgram", alias="STT_PROVIDER")
    stt_url: Optional[str] = Field("ws://livekit:43007", alias="STT_URL")
    voice_provider: Optional[str] = Field("11labs", alias="VOICE_PROVIDER")
    silence_threshold: Optional[float] = Field(15, alias="SILENCE_THRESHOLD")
    gpu_mode: Optional[bool] = Field(False, alias="GPU_MODE")

# LangChain configuration
class LangChainSettings(BaseSettings):
    langchain_tracing_v2: Optional[str] = Field(None, alias="LANGCHAIN_TRACING_V2")
    langchain_api_key: Optional[str] = Field(None, alias="LANGCHAIN_API_KEY")
    langchain_endpoint: Optional[str] = Field(None, alias="LANGCHAIN_ENDPOINT")

class Config(BaseSettings):
    env: Optional[str] = os.environ.get("ENVIRONMENT", "development").lower()
    core_api: Optional[CoreAPISettings] = None
    conversation_api: Optional[ConversationAPISettings] = None
    livekit: Optional[LiveKitSettings] = None
    langfuse: Optional[LangfuseSettings] = None
    openai: Optional[OpenAISettings] = None
    elevenlabs: Optional[ElevenLabsSettings]= None
    playai: Optional[PlayAiSettings] = None
    deepgram: Optional[DeepgramSettings]= None
    langchain: Optional[LangChainSettings]= None
    app: Optional[AppSettings]= None
    assemblyai: Optional[AssemblyAISettings] = None

    def load_config(self):
        self.core_api = CoreAPISettings()
        self.conversation_api = ConversationAPISettings()
        self.livekit = LiveKitSettings()
        self.langfuse = LangfuseSettings()
        self.openai = OpenAISettings()
        self.elevenlabs = ElevenLabsSettings()
        self.playai = PlayAiSettings()
        self.deepgram = DeepgramSettings()
        self.langchain = LangChainSettings()
        self.app = AppSettings()
        self.assemblyai = AssemblyAISettings()

    def log_configuration(self):
        print("Loaded Configuration:")
        print(f"Core API URL: {self.core_api.url}")
        print(f"Conversation API URL: {self.conversation_api.url}")
        print(f"LiveKit URL: {self.livekit.url}")
        print(f"Langfuse Public Key: {self.langfuse.public_key}")
        print(f"OpenAI Model: {self.openai.llm_model}")
        print(f"ElevenLabs Voice Name: {self.elevenlabs.elevenlabs_voice_name}")
        print(f"Deepgram API Key: {self.deepgram.deepgram_api_key}")
        print(f"App Log Level: {self.app.log_level}")

def get_config() -> Config:
    global _config
    if _config is None:
        _config = _load_config()  # Load the config once
    return _config

def _load_config() -> Config:
    try:
        conf = Config()
        conf.load_config()
        return conf
    except Exception as e:
        print(f"Error loading configuration: {str(e)}")
        raise
