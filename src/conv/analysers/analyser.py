import json
import logging
from typing import Dict, Any, Optional

from langfuse.decorators import observe
from livekit.agents import ChatContext, ChatMessage
from pydantic import BaseModel, Field

from conv.conv_meta import MetadataType
from conv.conv_svc import ConvSvc
from log.sentry_decorators import sentry_span

_logger = logging.getLogger(__name__)


class AnalyserConfig(BaseModel):
    enabled: bool = Field(True, alias="ANALYSERS_ENABLED")
    analysis_model: str = Field("gpt-4o", alias="ANALYSIS_MODEL")
    save_intermediate_results: bool = Field(True, alias="SAVE_INTERMEDIATE_RESULTS")
    prompt: Optional[str] = Field(
        default="{ \"call_notes\": { \"key_points\": [\"user asked\",\"agent responded\"] } }",
        alias="PROMPT"
    )

    def __init__(self, **data):
        super().__init__(**data)
        if data['prompt']:
            self.prompt = data['prompt']

class CallAnalyser:
    metrics: Dict[str, Any] = {}

    def __init__(self, config: AnalyserConfig, core_api_client: ConvSvc):
        self.conversation_history: Optional[ChatContext] = None
        self.metadata = {}
        self.core_api_client = core_api_client
        self.config = config
        # Use the model specified in config
        from livekit.plugins import openai
        self.llm = openai.LLM(model=config.analysis_model, temperature=0.1)

    @sentry_span(op="Agent.set_metadata", description="Agent.set_metadata")
    def set_metadata(self, metadata: Dict[str, Any]) -> None:
        self.metadata.update(metadata)

    @sentry_span(op="Agent.build_conversation_result", description="Agent.build_conversation_result")
    async def build_conversation_result(self, conversation_id: str, conversation_history: ChatContext) -> None:
        metadata_type = self.metadata.get('conversationType', 'test')
        is_test = any(test_type.value in metadata_type for test_type in [
            MetadataType.TEST_OUTBOUND_CAMPAIGN_CALL,
            MetadataType.TEST_CALL,
            MetadataType.INBOUND_TEST_CALL
        ])
        if is_test:
            return

        self.conversation_history = conversation_history
        result = await self._analyze_conversation()
        if not result:
            return

        try:
            await self.core_api_client.save_conversation_result(
                conversation_id=conversation_id,
                result=result,
                is_final=True
            )
            _logger.info(f"Final conversation result saved for conversation: {conversation_id}")
        except Exception as e:
            _logger.error(f"Final conversation result was not saved for conversation: {conversation_id} - {e}")

    @observe
    @sentry_span(op="Agent.analyze_conversation", description="Agent.analyze_conversation")
    async def _analyze_conversation(self) -> Optional[Dict[str, Any]]:
        if not self.conversation_history:
            return None

        conversation_text = "\n".join(
            [f"{msg.role}: {msg.content}" for msg in self.conversation_history.items]
        )

        base_prompt = (
            "User will provide you full conversation between another user and agent. You have to analyse the conversation provided, and return json using and reply ONLY with a valid JSON object, no explanations, "
            "no markdown, no extra comments or something similar. The resulting answer should be in JSON format and must match exactly this example: "
            f"{self.config.prompt} If you cannot fill a field, use null or an empty string. Reply ONLY with a valid JSON object, no markdown, no extra text. Do NOT use triple backticks"
        )

        messages = [
            ChatMessage(content=[base_prompt], role="system"),
            ChatMessage(content=[conversation_text], role="user"),
        ]
        ctx = ChatContext(items=messages)

        return await get_llm_json_object(self.llm, ctx, base_prompt, max_retries=3)


async def get_llm_json_object(
        llm,
        ctx: ChatContext,
        base_prompt: str,
        max_retries: int = 3
) -> Optional[Dict[str, Any]]:
    """
    Calls the LLM, attempts to parse the result as a JSON object.
    Retries up to `max_retries` times with stricter prompts if the result is not valid JSON.
    Returns the parsed object or None.
    """
    for attempt in range(max_retries):
        if attempt == 0:
            system_prompt = base_prompt
        else:
            system_prompt = (
                    "Your previous answer was not valid JSON. Reply ONLY with a valid JSON object, no markdown, no extra text. "
                    "Do NOT use triple backticks. " + base_prompt
            )

        # Update only the system message in ctx
        ctx.items[0].content = [system_prompt]

        try:
            stream = llm.chat(chat_ctx=ctx)
            response_parts = []
            async for chunk in stream:
                if hasattr(chunk, "delta") and getattr(chunk.delta, "content", None):
                    response_parts.append(chunk.delta.content)
                elif hasattr(chunk, "content"):
                    response_parts.append(chunk.content)
                elif isinstance(chunk, str):
                    response_parts.append(chunk)
            response_text = "".join(response_parts).strip()

            try:
                result = json.loads(response_text)
                return result
            except json.JSONDecodeError:
                _logger.warning(
                    f"Attempt {attempt + 1}: LLM response was not valid JSON. Retrying.\nOutput: {response_text}")
                continue

        except Exception as e:
            _logger.error(f"Error analyzing conversation or parsing JSON (attempt {attempt + 1}): {e}")

    _logger.error("Failed to get valid JSON from LLM after retries.")
    return None
